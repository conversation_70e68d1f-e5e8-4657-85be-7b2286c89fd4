﻿#pragma once
#include <thread>
#include <condition_variable>
#include <map>
#include <unordered_map>
#include <queue>
#include <memory>
#include <atomic>
#include <mutex>
#include <chrono>
#include <exception>
#include <optional>

namespace HHBUI
{
	/// <summary>
	/// [线程安全] 渲染线程基类 - 现代化设计
	/// 提供安全的线程管理、异常处理和资源自动清理
	/// </summary>
	class TOAPI UIRenderThread
	{
	public:
		/// <summary>
		/// 线程状态枚举
		/// </summary>
		enum class ThreadState : int
		{
			Stopped = 0,    // 已停止
			Running = 1,    // 运行中
			Paused = 2,     // 已暂停
			Stopping = 3    // 正在停止
		};

		UIRenderThread() noexcept;
		virtual ~UIRenderThread() noexcept;

		// 禁用拷贝构造和赋值
		UIRenderThread(const UIRenderThread&) = delete;
		UIRenderThread& operator=(const UIRenderThread&) = delete;

		// 支持移动语义
		UIRenderThread(UIRenderThread&& other) noexcept;
		UIRenderThread& operator=(UIRenderThread&& other) noexcept;

		/// <summary>
		/// 启动渲染线程
		/// </summary>
		/// <param name="pause">是否以暂停状态启动</param>
		/// <returns>是否成功启动</returns>
		[[nodiscard]] virtual bool Start(bool pause = false) noexcept;

		/// <summary>
		/// 暂停线程执行
		/// </summary>
		virtual void Pause() noexcept;

		/// <summary>
		/// 恢复线程执行
		/// </summary>
		virtual void Resume() noexcept;

		/// <summary>
		/// 停止线程并等待结束
		/// </summary>
		/// <param name="timeout_ms">超时时间(毫秒)，0表示无限等待</param>
		/// <returns>是否在超时时间内成功停止</returns>
		[[nodiscard]] virtual bool Stop(std::chrono::milliseconds timeout_ms = std::chrono::milliseconds{0}) noexcept;

		/// <summary>
		/// 获取当前线程状态
		/// </summary>
		[[nodiscard]] ThreadState GetState() const noexcept { return m_state.load(); }

		/// <summary>
		/// 检查线程是否正在运行
		/// </summary>
		[[nodiscard]] bool IsRunning() const noexcept { return GetState() == ThreadState::Running; }

		/// <summary>
		/// 检查线程是否已暂停
		/// </summary>
		[[nodiscard]] bool IsPaused() const noexcept { return GetState() == ThreadState::Paused; }

		/// <summary>
		/// 获取线程ID（如果线程正在运行）
		/// </summary>
		[[nodiscard]] std::optional<std::thread::id> GetThreadId() const noexcept;

	protected:
		/// <summary>
		/// 渲染线程主函数 - 子类必须实现
		/// 注意：此函数应该定期检查是否需要暂停或停止
		/// </summary>
		virtual void RenderThread() = 0;

		/// <summary>
		/// 检查是否应该继续运行
		/// 子类可以在RenderThread()中调用此函数来检查状态
		/// </summary>
		[[nodiscard]] bool ShouldContinue() const noexcept;

		/// <summary>
		/// 等待暂停状态结束
		/// 如果当前处于暂停状态，此函数会阻塞直到恢复或停止
		/// </summary>
		void WaitIfPaused();

	private:
		void ThreadMain() noexcept;
		void CleanupThread() noexcept;

		std::unique_ptr<std::thread> m_thread;
		mutable std::mutex m_mutex;
		std::condition_variable m_condition;
		std::atomic<ThreadState> m_state{ThreadState::Stopped};
		std::atomic<bool> m_stop_requested{false};
	};
	/// <summary>
	/// [线程安全] FPS计数器和帧率限制器 - 高精度性能监控
	/// 提供准确的FPS计算、帧时间统计和智能帧率限制
	/// </summary>
	class TOAPI UIFPSCounter
	{
	public:
		/// <summary>
		/// FPS统计信息结构
		/// </summary>
		struct FPSStats
		{
			float current_fps = 0.0f;          // 当前FPS
			float average_fps = 0.0f;          // 平均FPS
			float min_fps = 0.0f;              // 最小FPS
			float max_fps = 0.0f;              // 最大FPS
			double frame_time_ms = 0.0;        // 当前帧时间(毫秒)
			double average_frame_time_ms = 0.0; // 平均帧时间(毫秒)
			std::uint64_t total_frames = 0;    // 总帧数
		};

		UIFPSCounter() noexcept;
		~UIFPSCounter() noexcept = default;

		// 禁用拷贝，允许移动
		UIFPSCounter(const UIFPSCounter&) = delete;
		UIFPSCounter& operator=(const UIFPSCounter&) = delete;
		UIFPSCounter(UIFPSCounter&&) noexcept = default;
		UIFPSCounter& operator=(UIFPSCounter&&) noexcept = default;

		/// <summary>
		/// 标记新帧开始并计算FPS
		/// </summary>
		/// <returns>当前FPS值</returns>
		[[nodiscard]] float MarkFrame() noexcept;

		/// <summary>
		/// 获取详细的FPS统计信息
		/// </summary>
		[[nodiscard]] FPSStats GetStats() const noexcept;

		/// <summary>
		/// 设置目标FPS限制
		/// </summary>
		/// <param name="target_fps">目标FPS，<=0表示无限制</param>
		void SetTargetFPS(float target_fps) noexcept;

		/// <summary>
		/// 获取当前目标FPS
		/// </summary>
		[[nodiscard]] float GetTargetFPS() const noexcept;

		/// <summary>
		/// 执行帧率限制（如果设置了目标FPS）
		/// 此函数会阻塞当前线程直到达到目标帧时间
		/// </summary>
		void LimitFrameRate() noexcept;

		/// <summary>
		/// 重置所有统计数据
		/// </summary>
		void Reset() noexcept;

		/// <summary>
		/// 检查是否启用了帧率限制
		/// </summary>
		[[nodiscard]] bool IsFrameRateLimited() const noexcept;

	private:
		void UpdateStats() noexcept;
		void ResetMinMaxFPS() noexcept;

		// 使用高精度时钟
		using Clock = std::chrono::high_resolution_clock;
		using TimePoint = Clock::time_point;
		using Duration = Clock::duration;

		mutable std::mutex m_mutex;

		// FPS计算相关
		float m_current_fps = 0.0f;
		float m_target_fps = 0.0f;
		std::uint32_t m_frame_count = 0;
		TimePoint m_last_fps_update = Clock::now();

		// 帧时间统计
		TimePoint m_last_frame_time = Clock::now();
		TimePoint m_frame_start_time = Clock::now();
		Duration m_target_frame_duration{};

		// 统计数据
		float m_min_fps = std::numeric_limits<float>::max();
		float m_max_fps = 0.0f;
		double m_total_frame_time = 0.0;
		std::uint64_t m_total_frames = 0;

		// 性能优化：避免频繁的时间计算
		static constexpr auto FPS_UPDATE_INTERVAL = std::chrono::milliseconds{250};
		static constexpr float MIN_VALID_FPS = 0.1f;
		static constexpr float MAX_VALID_FPS = 10000.0f;
	};
	/// <summary>
	/// [线程安全] 高性能队列容器 - 支持多生产者多消费者
	/// 提供完整的异常安全保证和现代C++接口
	/// </summary>
	template <typename T>
	class TOAPI UIQueue
	{
	public:
		using value_type = T;
		using size_type = std::size_t;
		using reference = T&;
		using const_reference = const T&;

		/// <summary>
		/// 构造函数
		/// </summary>
		UIQueue() noexcept = default;

		/// <summary>
		/// 带容量限制的构造函数
		/// </summary>
		/// <param name="max_capacity">最大容量，0表示无限制</param>
		explicit UIQueue(size_type max_capacity) noexcept
			: m_max_capacity(max_capacity) {}

		~UIQueue() noexcept = default;

		// 禁用拷贝构造和赋值
		UIQueue(const UIQueue&) = delete;
		UIQueue& operator=(const UIQueue&) = delete;

		// 支持移动语义
		UIQueue(UIQueue&& other) noexcept
		{
			std::lock_guard<std::mutex> lock(other.m_mutex);
			m_queue = std::move(other.m_queue);
			m_max_capacity = other.m_max_capacity;
		}

		UIQueue& operator=(UIQueue&& other) noexcept
		{
			if (this != &other)
			{
				std::lock(m_mutex, other.m_mutex);
				std::lock_guard<std::mutex> lock1(m_mutex, std::adopt_lock);
				std::lock_guard<std::mutex> lock2(other.m_mutex, std::adopt_lock);

				m_queue = std::move(other.m_queue);
				m_max_capacity = other.m_max_capacity;
			}
			return *this;
		}

		/// <summary>
		/// 检查队列是否为空
		/// </summary>
		[[nodiscard]] bool empty() const noexcept
		{
			std::lock_guard<std::mutex> lock(m_mutex);
			return m_queue.empty();
		}

		/// <summary>
		/// 获取队列大小
		/// </summary>
		[[nodiscard]] size_type size() const noexcept
		{
			std::lock_guard<std::mutex> lock(m_mutex);
			return m_queue.size();
		}

		/// <summary>
		/// 获取最大容量
		/// </summary>
		[[nodiscard]] size_type max_capacity() const noexcept
		{
			return m_max_capacity;
		}

		/// <summary>
		/// 检查队列是否已满
		/// </summary>
		[[nodiscard]] bool full() const noexcept
		{
			if (m_max_capacity == 0) return false;
			std::lock_guard<std::mutex> lock(m_mutex);
			return m_queue.size() >= m_max_capacity;
		}

		/// <summary>
		/// 在队列前端插入元素（高优先级）
		/// </summary>
		template<typename U>
		[[nodiscard]] bool push_front(U&& item) noexcept
		{
			try
			{
				std::lock_guard<std::mutex> lock(m_mutex);
				if (m_max_capacity > 0 && m_queue.size() >= m_max_capacity)
					return false;

				m_queue.emplace_front(std::forward<U>(item));
				return true;
			}
			catch (...)
			{
				return false;
			}
		}

		/// <summary>
		/// 在队列后端插入元素（正常优先级）
		/// </summary>
		template<typename U>
		[[nodiscard]] bool push_back(U&& item) noexcept
		{
			try
			{
				std::lock_guard<std::mutex> lock(m_mutex);
				if (m_max_capacity > 0 && m_queue.size() >= m_max_capacity)
					return false;

				m_queue.emplace_back(std::forward<U>(item));
				return true;
			}
			catch (...)
			{
				return false;
			}
		}

		/// <summary>
		/// 从队列前端取出元素
		/// </summary>
		[[nodiscard]] std::optional<T> pop_front() noexcept
		{
			try
			{
				std::lock_guard<std::mutex> lock(m_mutex);
				if (m_queue.empty())
					return std::nullopt;

				T item = std::move(m_queue.front());
				m_queue.pop_front();
				return item;
			}
			catch (...)
			{
				return std::nullopt;
			}
		}

		/// <summary>
		/// 从队列后端取出元素
		/// </summary>
		[[nodiscard]] std::optional<T> pop_back() noexcept
		{
			try
			{
				std::lock_guard<std::mutex> lock(m_mutex);
				if (m_queue.empty())
					return std::nullopt;

				T item = std::move(m_queue.back());
				m_queue.pop_back();
				return item;
			}
			catch (...)
			{
				return std::nullopt;
			}
		}

		/// <summary>
		/// 清空队列
		/// </summary>
		void clear() noexcept
		{
			std::lock_guard<std::mutex> lock(m_mutex);
			m_queue.clear();
		}

		/// <summary>
		/// 批量操作：获取多个元素
		/// </summary>
		template<typename OutputIt>
		size_type pop_multiple(OutputIt out, size_type max_count) noexcept
		{
			try
			{
				std::lock_guard<std::mutex> lock(m_mutex);
				size_type count = std::min(max_count, m_queue.size());

				for (size_type i = 0; i < count; ++i)
				{
					*out++ = std::move(m_queue.front());
					m_queue.pop_front();
				}

				return count;
			}
			catch (...)
			{
				return 0;
			}
		}

		// 兼容旧接口（已弃用，建议使用新接口）
		[[deprecated("Use push_front() instead")]]
		void insertquque(T& t) { push_front(t); }

		[[deprecated("Use push_back() instead")]]
		void enqueue(T& t) { push_back(t); }

		[[deprecated("Use pop_front() instead")]]
		bool dequeue(T& t)
		{
			auto result = pop_front();
			if (result)
			{
				t = std::move(*result);
				return true;
			}
			return false;
		}

	private:
		mutable std::mutex m_mutex;
		std::deque<T> m_queue;  // 使用deque替代list，提供更好的性能
		size_type m_max_capacity = 0;  // 0表示无限制
	};
	/// <summary>
	/// UI对象基类 - 现代化设计
	/// 提供类型安全的基础接口和资源管理
	/// </summary>
	class TOAPI UIBase
	{
	public:
		UIBase() noexcept = default;
		virtual ~UIBase() noexcept = default;

		// 禁用拷贝构造和赋值
		UIBase(const UIBase&) = delete;
		UIBase& operator=(const UIBase&) = delete;

		// 支持移动语义
		UIBase(UIBase&& other) noexcept
			: m_ui_view(std::exchange(other.m_ui_view, nullptr))
			, m_ui_window(std::exchange(other.m_ui_window, nullptr))
			, m_child_first(std::exchange(other.m_child_first, nullptr))
			, m_child_last(std::exchange(other.m_child_last, nullptr))
		{
		}

		UIBase& operator=(UIBase&& other) noexcept
		{
			if (this != &other)
			{
				m_ui_view = std::exchange(other.m_ui_view, nullptr);
				m_ui_window = std::exchange(other.m_ui_window, nullptr);
				m_child_first = std::exchange(other.m_child_first, nullptr);
				m_child_last = std::exchange(other.m_child_last, nullptr);
			}
			return *this;
		}

		/// <summary>
		/// 获取UI视图指针
		/// </summary>
		[[nodiscard]] void* GetUIView() const noexcept { return m_ui_view; }

		/// <summary>
		/// 设置UI视图指针
		/// </summary>
		void SetUIView(void* view) noexcept { m_ui_view = view; }

		/// <summary>
		/// 获取UI窗口指针
		/// </summary>
		[[nodiscard]] void* GetUIWindow() const noexcept { return m_ui_window; }

		/// <summary>
		/// 设置UI窗口指针
		/// </summary>
		void SetUIWindow(void* window) noexcept { m_ui_window = window; }

		/// <summary>
		/// 类型安全的模板方法：获取指定类型的UI视图
		/// </summary>
		template<typename T>
		[[nodiscard]] T* GetUIViewAs() const noexcept
		{
			return static_cast<T*>(m_ui_view);
		}

		/// <summary>
		/// 类型安全的模板方法：获取指定类型的UI窗口
		/// </summary>
		template<typename T>
		[[nodiscard]] T* GetUIWindowAs() const noexcept
		{
			return static_cast<T*>(m_ui_window);
		}

		// 兼容旧接口（已弃用）
		[[deprecated("Use GetUIView() instead")]]
		LPVOID m_UIView = nullptr;

		[[deprecated("Use GetUIWindow() instead")]]
		LPVOID m_UIWindow = nullptr;

	protected:
		/// <summary>
		/// 获取第一个子对象
		/// </summary>
		[[nodiscard]] void* GetFirstChild() const noexcept { return m_child_first; }

		/// <summary>
		/// 获取最后一个子对象
		/// </summary>
		[[nodiscard]] void* GetLastChild() const noexcept { return m_child_last; }

		/// <summary>
		/// 设置第一个子对象
		/// </summary>
		void SetFirstChild(void* child) noexcept { m_child_first = child; }

		/// <summary>
		/// 设置最后一个子对象
		/// </summary>
		void SetLastChild(void* child) noexcept { m_child_last = child; }

		// 兼容旧接口（已弃用）
		[[deprecated("Use GetFirstChild()/SetFirstChild() instead")]]
		LPVOID m_objChildFirst = nullptr;

		[[deprecated("Use GetLastChild()/SetLastChild() instead")]]
		LPVOID m_objChildLast = nullptr;

		friend class UIWnd;
		friend class UIControl;
		friend class UICanvas;
		friend class UILayout;
		friend class UIAnimation;

	private:
		void* m_ui_view = nullptr;
		void* m_ui_window = nullptr;
		void* m_child_first = nullptr;
		void* m_child_last = nullptr;
	};

	/// <summary>
	/// 定时器信息类 - 现代化RAII设计
	/// 提供自动资源管理和异常安全
	/// </summary>
	class TOAPI TimerInfo
	{
	public:
		TimerInfo() noexcept = default;

		TimerInfo(UIBase* prop_obj, std::size_t local_id, HWND hwnd, UINT win_timer) noexcept
			: m_prop_obj(prop_obj)
			, m_local_id(local_id)
			, m_hwnd(hwnd)
			, m_win_timer(win_timer)
			, m_killed(false)
		{
		}

		~TimerInfo() noexcept
		{
			Kill();
		}

		// 禁用拷贝构造和赋值
		TimerInfo(const TimerInfo&) = delete;
		TimerInfo& operator=(const TimerInfo&) = delete;

		// 支持移动语义
		TimerInfo(TimerInfo&& other) noexcept
			: m_prop_obj(std::exchange(other.m_prop_obj, nullptr))
			, m_local_id(std::exchange(other.m_local_id, 0))
			, m_hwnd(std::exchange(other.m_hwnd, nullptr))
			, m_win_timer(std::exchange(other.m_win_timer, 0))
			, m_killed(std::exchange(other.m_killed, true))
		{
		}

		TimerInfo& operator=(TimerInfo&& other) noexcept
		{
			if (this != &other)
			{
				Kill(); // 清理当前资源

				m_prop_obj = std::exchange(other.m_prop_obj, nullptr);
				m_local_id = std::exchange(other.m_local_id, 0);
				m_hwnd = std::exchange(other.m_hwnd, nullptr);
				m_win_timer = std::exchange(other.m_win_timer, 0);
				m_killed = std::exchange(other.m_killed, true);
			}
			return *this;
		}

		/// <summary>
		/// 获取属性对象
		/// </summary>
		[[nodiscard]] UIBase* GetPropObj() const noexcept { return m_prop_obj; }

		/// <summary>
		/// 获取本地ID
		/// </summary>
		[[nodiscard]] std::size_t GetLocalId() const noexcept { return m_local_id; }

		/// <summary>
		/// 获取窗口句柄
		/// </summary>
		[[nodiscard]] HWND GetHWnd() const noexcept { return m_hwnd; }

		/// <summary>
		/// 获取Windows定时器ID
		/// </summary>
		[[nodiscard]] UINT GetWinTimer() const noexcept { return m_win_timer; }

		/// <summary>
		/// 检查定时器是否已被销毁
		/// </summary>
		[[nodiscard]] bool IsKilled() const noexcept { return m_killed; }

		/// <summary>
		/// 销毁定时器
		/// </summary>
		void Kill() noexcept
		{
			if (!m_killed && m_hwnd && m_win_timer)
			{
				::KillTimer(m_hwnd, m_win_timer);
				m_killed = true;
			}
		}

		/// <summary>
		/// 重置定时器信息
		/// </summary>
		void Reset(UIBase* prop_obj, std::size_t local_id, HWND hwnd, UINT win_timer) noexcept
		{
			Kill(); // 先清理旧的定时器

			m_prop_obj = prop_obj;
			m_local_id = local_id;
			m_hwnd = hwnd;
			m_win_timer = win_timer;
			m_killed = false;
		}

		// 兼容旧接口（已弃用）
		[[deprecated("Use GetPropObj() instead")]]
		UIBase* pPropObj = nullptr;

		[[deprecated("Use GetLocalId() instead")]]
		std::size_t nLocalID = 0;

		[[deprecated("Use GetHWnd() instead")]]
		HWND hWnd = nullptr;

		[[deprecated("Use GetWinTimer() instead")]]
		UINT uWinTimer = 0;

		[[deprecated("Use IsKilled() instead")]]
		bool bKilled = false;

	private:
		UIBase* m_prop_obj = nullptr;
		std::size_t m_local_id = 0;
		HWND m_hwnd = nullptr;
		UINT m_win_timer = 0;
		bool m_killed = false;
	};

	/// <summary>
	/// 定时器信息容器类型
	/// </summary>
	using TimerInfoVector = std::vector<TimerInfo>;

	// 兼容旧类型定义（已弃用）
	[[deprecated("Use TimerInfoVector instead")]]
	using VecTimerInfo = std::vector<TimerInfo>;

	// ============================================================================
	// Modern C++17 Utilities and Helper Functions
	// ============================================================================

	/// <summary>
	/// 线程安全的单例模板 - 使用C++17特性
	/// </summary>
	template<typename T>
	class ThreadSafeSingleton
	{
	public:
		template<typename... Args>
		[[nodiscard]] static T& GetInstance(Args&&... args) noexcept(std::is_nothrow_constructible_v<T, Args...>)
		{
			static T instance(std::forward<Args>(args)...);
			return instance;
		}

		ThreadSafeSingleton(const ThreadSafeSingleton&) = delete;
		ThreadSafeSingleton& operator=(const ThreadSafeSingleton&) = delete;
		ThreadSafeSingleton(ThreadSafeSingleton&&) = delete;
		ThreadSafeSingleton& operator=(ThreadSafeSingleton&&) = delete;

	protected:
		ThreadSafeSingleton() = default;
		virtual ~ThreadSafeSingleton() = default;
	};

	/// <summary>
	/// RAII作用域守卫 - 自动执行清理操作
	/// </summary>
	template<typename Func>
	class ScopeGuard
	{
	public:
		explicit ScopeGuard(Func&& func) noexcept(std::is_nothrow_move_constructible_v<Func>)
			: m_func(std::move(func)), m_active(true) {}

		~ScopeGuard() noexcept
		{
			if (m_active)
			{
				try { m_func(); }
				catch (...) { /* 忽略析构函数中的异常 */ }
			}
		}

		void Dismiss() noexcept { m_active = false; }

		ScopeGuard(const ScopeGuard&) = delete;
		ScopeGuard& operator=(const ScopeGuard&) = delete;
		ScopeGuard(ScopeGuard&& other) noexcept
			: m_func(std::move(other.m_func)), m_active(std::exchange(other.m_active, false)) {}

	private:
		Func m_func;
		bool m_active;
	};

	/// <summary>
	/// 创建作用域守卫的便利函数
	/// </summary>
	template<typename Func>
	[[nodiscard]] auto MakeScopeGuard(Func&& func) noexcept(std::is_nothrow_constructible_v<ScopeGuard<std::decay_t<Func>>, Func>)
	{
		return ScopeGuard<std::decay_t<Func>>(std::forward<Func>(func));
	}

	/// <summary>
	/// 类型安全的枚举类工具
	/// </summary>
	template<typename Enum>
	constexpr auto ToUnderlying(Enum e) noexcept -> std::underlying_type_t<Enum>
	{
		return static_cast<std::underlying_type_t<Enum>>(e);
	}

	/// <summary>
	/// 安全的数值转换 - 检查溢出
	/// </summary>
	template<typename To, typename From>
	[[nodiscard]] constexpr std::optional<To> SafeCast(From value) noexcept
	{
		if constexpr (std::is_same_v<To, From>)
		{
			return value;
		}
		else if constexpr (std::is_integral_v<To> && std::is_integral_v<From>)
		{
			if (value >= std::numeric_limits<To>::min() && value <= std::numeric_limits<To>::max())
			{
				return static_cast<To>(value);
			}
		}
		else if constexpr (std::is_floating_point_v<To> && std::is_arithmetic_v<From>)
		{
			return static_cast<To>(value);
		}
		return std::nullopt;
	}

	/// <summary>
	/// 字符串工具函数
	/// </summary>
	namespace StringUtils
	{
		/// <summary>
		/// 安全的字符串比较 - 处理nullptr
		/// </summary>
		[[nodiscard]] inline bool SafeCompare(const wchar_t* lhs, const wchar_t* rhs) noexcept
		{
			if (lhs == rhs) return true;
			if (!lhs || !rhs) return false;
			return std::wcscmp(lhs, rhs) == 0;
		}

		/// <summary>
		/// 检查字符串是否为空或nullptr
		/// </summary>
		[[nodiscard]] constexpr bool IsNullOrEmpty(const wchar_t* str) noexcept
		{
			return !str || *str == L'\0';
		}

		/// <summary>
		/// 安全的字符串长度计算
		/// </summary>
		[[nodiscard]] constexpr std::size_t SafeLength(const wchar_t* str) noexcept
		{
			return str ? std::wcslen(str) : 0;
		}
	}

	/// <summary>
	/// 性能计时器 - 高精度时间测量
	/// </summary>
	class PerformanceTimer
	{
	public:
		using Clock = std::chrono::high_resolution_clock;
		using TimePoint = Clock::time_point;
		using Duration = Clock::duration;

		PerformanceTimer() noexcept : m_start(Clock::now()) {}

		void Reset() noexcept { m_start = Clock::now(); }

		[[nodiscard]] auto Elapsed() const noexcept -> Duration
		{
			return Clock::now() - m_start;
		}

		[[nodiscard]] double ElapsedMilliseconds() const noexcept
		{
			return std::chrono::duration<double, std::milli>(Elapsed()).count();
		}

		[[nodiscard]] double ElapsedMicroseconds() const noexcept
		{
			return std::chrono::duration<double, std::micro>(Elapsed()).count();
		}

	private:
		TimePoint m_start;
	};

	/// <summary>
	/// 内存对齐工具
	/// </summary>
	template<std::size_t Alignment>
	[[nodiscard]] constexpr std::size_t AlignUp(std::size_t size) noexcept
	{
		static_assert((Alignment & (Alignment - 1)) == 0, "Alignment must be power of 2");
		return (size + Alignment - 1) & ~(Alignment - 1);
	}

	/// <summary>
	/// 检查指针是否对齐
	/// </summary>
	template<std::size_t Alignment>
	[[nodiscard]] constexpr bool IsAligned(const void* ptr) noexcept
	{
		static_assert((Alignment & (Alignment - 1)) == 0, "Alignment must be power of 2");
		return (reinterpret_cast<std::uintptr_t>(ptr) & (Alignment - 1)) == 0;
	}

	// ============================================================================
	// Error Handling and Exception Safety
	// ============================================================================

	/// <summary>
	/// 错误代码枚举
	/// </summary>
	enum class ErrorCode : std::uint32_t
	{
		Success = 0,
		InvalidParameter = 1,
		OutOfMemory = 2,
		ThreadError = 3,
		TimeoutError = 4,
		ResourceBusy = 5,
		NotInitialized = 6,
		AlreadyInitialized = 7,
		OperationFailed = 8,
		NotSupported = 9,
		AccessDenied = 10,
		Unknown = 0xFFFFFFFF
	};

	/// <summary>
	/// 结果类型 - 类似Rust的Result<T, E>
	/// </summary>
	template<typename T, typename E = ErrorCode>
	class Result
	{
	public:
		// 成功构造函数
		Result(T&& value) noexcept(std::is_nothrow_move_constructible_v<T>)
			: m_has_value(true)
		{
			new(&m_storage.value) T(std::move(value));
		}

		Result(const T& value) noexcept(std::is_nothrow_copy_constructible_v<T>)
			: m_has_value(true)
		{
			new(&m_storage.value) T(value);
		}

		// 错误构造函数
		Result(E&& error) noexcept(std::is_nothrow_move_constructible_v<E>)
			: m_has_value(false)
		{
			new(&m_storage.error) E(std::move(error));
		}

		Result(const E& error) noexcept(std::is_nothrow_copy_constructible_v<E>)
			: m_has_value(false)
		{
			new(&m_storage.error) E(error);
		}

		~Result() noexcept
		{
			if (m_has_value)
				m_storage.value.~T();
			else
				m_storage.error.~E();
		}

		// 拷贝构造
		Result(const Result& other) noexcept(std::is_nothrow_copy_constructible_v<T> && std::is_nothrow_copy_constructible_v<E>)
			: m_has_value(other.m_has_value)
		{
			if (m_has_value)
				new(&m_storage.value) T(other.m_storage.value);
			else
				new(&m_storage.error) E(other.m_storage.error);
		}

		// 移动构造
		Result(Result&& other) noexcept(std::is_nothrow_move_constructible_v<T> && std::is_nothrow_move_constructible_v<E>)
			: m_has_value(other.m_has_value)
		{
			if (m_has_value)
				new(&m_storage.value) T(std::move(other.m_storage.value));
			else
				new(&m_storage.error) E(std::move(other.m_storage.error));
		}

		// 赋值操作符
		Result& operator=(const Result& other) noexcept(std::is_nothrow_copy_constructible_v<T> && std::is_nothrow_copy_constructible_v<E>)
		{
			if (this != &other)
			{
				this->~Result();
				new(this) Result(other);
			}
			return *this;
		}

		Result& operator=(Result&& other) noexcept(std::is_nothrow_move_constructible_v<T> && std::is_nothrow_move_constructible_v<E>)
		{
			if (this != &other)
			{
				this->~Result();
				new(this) Result(std::move(other));
			}
			return *this;
		}

		/// <summary>
		/// 检查是否包含值
		/// </summary>
		[[nodiscard]] bool HasValue() const noexcept { return m_has_value; }
		[[nodiscard]] bool IsError() const noexcept { return !m_has_value; }
		[[nodiscard]] explicit operator bool() const noexcept { return m_has_value; }

		/// <summary>
		/// 获取值（如果存在）
		/// </summary>
		[[nodiscard]] T& Value() & { return m_storage.value; }
		[[nodiscard]] const T& Value() const & { return m_storage.value; }
		[[nodiscard]] T&& Value() && { return std::move(m_storage.value); }

		/// <summary>
		/// 获取错误（如果存在）
		/// </summary>
		[[nodiscard]] E& Error() & { return m_storage.error; }
		[[nodiscard]] const E& Error() const & { return m_storage.error; }
		[[nodiscard]] E&& Error() && { return std::move(m_storage.error); }

		/// <summary>
		/// 获取值或默认值
		/// </summary>
		template<typename U>
		[[nodiscard]] T ValueOr(U&& default_value) const &
		{
			return m_has_value ? m_storage.value : static_cast<T>(std::forward<U>(default_value));
		}

		template<typename U>
		[[nodiscard]] T ValueOr(U&& default_value) &&
		{
			return m_has_value ? std::move(m_storage.value) : static_cast<T>(std::forward<U>(default_value));
		}

	private:
		union Storage
		{
			T value;
			E error;
			Storage() {}
			~Storage() {}
		} m_storage;
		bool m_has_value;
	};

	/// <summary>
	/// 创建成功结果的便利函数
	/// </summary>
	template<typename T>
	[[nodiscard]] auto MakeResult(T&& value) noexcept(std::is_nothrow_constructible_v<Result<std::decay_t<T>>, T>)
	{
		return Result<std::decay_t<T>>(std::forward<T>(value));
	}

	/// <summary>
	/// 创建错误结果的便利函数
	/// </summary>
	template<typename T, typename E>
	[[nodiscard]] auto MakeError(E&& error) noexcept(std::is_nothrow_constructible_v<Result<T, std::decay_t<E>>, std::decay_t<E>>)
	{
		return Result<T, std::decay_t<E>>(std::forward<E>(error));
	}

	/// <summary>
	/// 异常安全的资源管理器
	/// </summary>
	template<typename Resource, typename Deleter>
	class UniqueResource
	{
	public:
		UniqueResource() noexcept = default;

		explicit UniqueResource(Resource&& resource, Deleter&& deleter) noexcept
			: m_resource(std::move(resource))
			, m_deleter(std::move(deleter))
			, m_valid(true)
		{
		}

		~UniqueResource() noexcept
		{
			if (m_valid)
			{
				try { m_deleter(m_resource); }
				catch (...) { /* 忽略析构函数中的异常 */ }
			}
		}

		UniqueResource(const UniqueResource&) = delete;
		UniqueResource& operator=(const UniqueResource&) = delete;

		UniqueResource(UniqueResource&& other) noexcept
			: m_resource(std::move(other.m_resource))
			, m_deleter(std::move(other.m_deleter))
			, m_valid(std::exchange(other.m_valid, false))
		{
		}

		UniqueResource& operator=(UniqueResource&& other) noexcept
		{
			if (this != &other)
			{
				Reset();
				m_resource = std::move(other.m_resource);
				m_deleter = std::move(other.m_deleter);
				m_valid = std::exchange(other.m_valid, false);
			}
			return *this;
		}

		[[nodiscard]] Resource& Get() noexcept { return m_resource; }
		[[nodiscard]] const Resource& Get() const noexcept { return m_resource; }
		[[nodiscard]] Resource* operator->() noexcept { return &m_resource; }
		[[nodiscard]] const Resource* operator->() const noexcept { return &m_resource; }
		[[nodiscard]] Resource& operator*() noexcept { return m_resource; }
		[[nodiscard]] const Resource& operator*() const noexcept { return m_resource; }

		[[nodiscard]] bool IsValid() const noexcept { return m_valid; }
		[[nodiscard]] explicit operator bool() const noexcept { return m_valid; }

		void Reset() noexcept
		{
			if (m_valid)
			{
				try { m_deleter(m_resource); }
				catch (...) { /* 忽略异常 */ }
				m_valid = false;
			}
		}

		Resource Release() noexcept
		{
			m_valid = false;
			return std::move(m_resource);
		}

	private:
		Resource m_resource{};
		Deleter m_deleter{};
		bool m_valid = false;
	};

	/// <summary>
	/// 创建唯一资源的便利函数
	/// </summary>
	template<typename Resource, typename Deleter>
	[[nodiscard]] auto MakeUniqueResource(Resource&& resource, Deleter&& deleter) noexcept
	{
		return UniqueResource<std::decay_t<Resource>, std::decay_t<Deleter>>(
			std::forward<Resource>(resource), std::forward<Deleter>(deleter));
	}

} // namespace HHBUI