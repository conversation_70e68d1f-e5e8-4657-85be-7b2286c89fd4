#include "pch.h"
#include "base.h"
#include <chrono>
#include <thread>
#include <algorithm>
#include <limits>

namespace HHBUI
{
	// ============================================================================
	// UIRenderThread Implementation
	// ============================================================================

	UIRenderThread::UIRenderThread() noexcept
		: m_thread(nullptr)
		, m_state(ThreadState::Stopped)
		, m_stop_requested(false)
	{
	}

	UIRenderThread::~UIRenderThread() noexcept
	{
		Stop();
	}

	UIRenderThread::UIRenderThread(UIRenderThread&& other) noexcept
		: m_thread(std::move(other.m_thread))
		, m_state(other.m_state.load())
		, m_stop_requested(other.m_stop_requested.load())
	{
		other.m_state = ThreadState::Stopped;
		other.m_stop_requested = false;
	}

	UIRenderThread& UIRenderThread::operator=(UIRenderThread&& other) noexcept
	{
		if (this != &other)
		{
			Stop(); // 停止当前线程

			m_thread = std::move(other.m_thread);
			m_state = other.m_state.load();
			m_stop_requested = other.m_stop_requested.load();

			other.m_state = ThreadState::Stopped;
			other.m_stop_requested = false;
		}
		return *this;
	}

	bool UIRenderThread::Start(bool pause) noexcept
	{
		try
		{
			std::lock_guard<std::mutex> lock(m_mutex);

			if (m_state != ThreadState::Stopped)
				return false;

			m_stop_requested = false;
			m_state = pause ? ThreadState::Paused : ThreadState::Running;

			m_thread = std::make_unique<std::thread>(&UIRenderThread::ThreadMain, this);
			return true;
		}
		catch (...)
		{
			m_state = ThreadState::Stopped;
			return false;
		}
	}

	void UIRenderThread::Pause() noexcept
	{
		ThreadState expected = ThreadState::Running;
		m_state.compare_exchange_strong(expected, ThreadState::Paused);
	}

	void UIRenderThread::Resume() noexcept
	{
		{
			std::lock_guard<std::mutex> lock(m_mutex);
			ThreadState expected = ThreadState::Paused;
			if (m_state.compare_exchange_strong(expected, ThreadState::Running))
			{
				m_condition.notify_one();
			}
		}
	}

	bool UIRenderThread::Stop(std::chrono::milliseconds timeout_ms) noexcept
	{
		try
		{
			std::unique_lock<std::mutex> lock(m_mutex);

			if (m_state == ThreadState::Stopped || !m_thread)
				return true;

			// 设置停止标志
			m_stop_requested = true;
			m_state = ThreadState::Stopping;

			// 唤醒可能在等待的线程
			m_condition.notify_all();
			lock.unlock();

			// 等待线程结束
			if (m_thread->joinable())
			{
				if (timeout_ms.count() > 0)
				{
					// 有超时限制的等待
					auto future = std::async(std::launch::async, [this]() {
						m_thread->join();
					});

					if (future.wait_for(timeout_ms) == std::future_status::timeout)
					{
						// 超时，强制终止（不推荐，但作为最后手段）
						return false;
					}
				}
				else
				{
					// 无限等待
					m_thread->join();
				}
			}

			CleanupThread();
			return true;
		}
		catch (...)
		{
			CleanupThread();
			return false;
		}
	}

	std::optional<std::thread::id> UIRenderThread::GetThreadId() const noexcept
	{
		std::lock_guard<std::mutex> lock(m_mutex);
		if (m_thread && m_thread->joinable())
		{
			return m_thread->get_id();
		}
		return std::nullopt;
	}

	bool UIRenderThread::ShouldContinue() const noexcept
	{
		return !m_stop_requested.load() && m_state.load() != ThreadState::Stopping;
	}

	void UIRenderThread::WaitIfPaused()
	{
		std::unique_lock<std::mutex> lock(m_mutex);
		m_condition.wait(lock, [this]() {
			return m_state.load() != ThreadState::Paused || m_stop_requested.load();
		});
	}

	void UIRenderThread::ThreadMain() noexcept
	{
		try
		{
			while (ShouldContinue())
			{
				// 检查是否需要暂停
				if (m_state.load() == ThreadState::Paused)
				{
					WaitIfPaused();
					continue;
				}

				// 执行渲染逻辑
				if (ShouldContinue())
				{
					RenderThread();
				}
			}
		}
		catch (...)
		{
			// 捕获所有异常，防止线程异常终止
		}

		m_state = ThreadState::Stopped;
	}

	void UIRenderThread::CleanupThread() noexcept
	{
		m_thread.reset();
		m_state = ThreadState::Stopped;
		m_stop_requested = false;
	}

	// ============================================================================
	// UIFPSCounter Implementation
	// ============================================================================

	UIFPSCounter::UIFPSCounter() noexcept
		: m_current_fps(0.0f)
		, m_target_fps(0.0f)
		, m_frame_count(0)
		, m_last_fps_update(Clock::now())
		, m_last_frame_time(Clock::now())
		, m_frame_start_time(Clock::now())
		, m_target_frame_duration{}
		, m_min_fps(std::numeric_limits<float>::max())
		, m_max_fps(0.0f)
		, m_total_frame_time(0.0)
		, m_total_frames(0)
	{
	}

	float UIFPSCounter::MarkFrame() noexcept
	{
		std::lock_guard<std::mutex> lock(m_mutex);

		const auto current_time = Clock::now();
		const auto frame_duration = current_time - m_last_frame_time;
		m_last_frame_time = current_time;

		// 更新帧计数和总时间
		++m_frame_count;
		++m_total_frames;
		m_total_frame_time += std::chrono::duration<double, std::milli>(frame_duration).count();

		// 检查是否需要更新FPS
		const auto time_since_update = current_time - m_last_fps_update;
		if (time_since_update >= FPS_UPDATE_INTERVAL)
		{
			UpdateStats();
			m_last_fps_update = current_time;
		}

		return m_current_fps;
	}
	UIFPSCounter::FPSStats UIFPSCounter::GetStats() const noexcept
	{
		std::lock_guard<std::mutex> lock(m_mutex);

		FPSStats stats;
		stats.current_fps = m_current_fps;
		stats.min_fps = (m_min_fps == std::numeric_limits<float>::max()) ? 0.0f : m_min_fps;
		stats.max_fps = m_max_fps;
		stats.total_frames = m_total_frames;

		if (m_total_frames > 0)
		{
			stats.average_fps = static_cast<float>(m_total_frames * 1000.0 / m_total_frame_time);
			stats.average_frame_time_ms = m_total_frame_time / m_total_frames;
		}

		// 计算当前帧时间
		if (m_current_fps > MIN_VALID_FPS)
		{
			stats.frame_time_ms = 1000.0 / m_current_fps;
		}

		return stats;
	}

	void UIFPSCounter::SetTargetFPS(float target_fps) noexcept
	{
		std::lock_guard<std::mutex> lock(m_mutex);

		if (target_fps <= 0.0f)
		{
			m_target_fps = 0.0f;
			m_target_frame_duration = Duration{};
		}
		else
		{
			m_target_fps = std::clamp(target_fps, MIN_VALID_FPS, MAX_VALID_FPS);
			m_target_frame_duration = std::chrono::duration_cast<Duration>(
				std::chrono::duration<double>(1.0 / m_target_fps)
			);
		}

		// 重置帧开始时间
		m_frame_start_time = Clock::now();
	}

	float UIFPSCounter::GetTargetFPS() const noexcept
	{
		std::lock_guard<std::mutex> lock(m_mutex);
		return m_target_fps;
	}

	void UIFPSCounter::LimitFrameRate() noexcept
	{
		if (!IsFrameRateLimited())
			return;

		try
		{
			std::lock_guard<std::mutex> lock(m_mutex);

			const auto current_time = Clock::now();
			const auto elapsed = current_time - m_frame_start_time;

			if (elapsed < m_target_frame_duration)
			{
				const auto sleep_duration = m_target_frame_duration - elapsed;
				lock.~lock_guard(); // 释放锁，避免在睡眠时持有锁

				std::this_thread::sleep_for(sleep_duration);
			}

			// 更新下一帧的开始时间
			m_frame_start_time = Clock::now();
		}
		catch (...)
		{
			// 忽略睡眠异常
		}
	}

	void UIFPSCounter::Reset() noexcept
	{
		std::lock_guard<std::mutex> lock(m_mutex);

		m_current_fps = 0.0f;
		m_frame_count = 0;
		m_last_fps_update = Clock::now();
		m_last_frame_time = Clock::now();
		m_frame_start_time = Clock::now();

		ResetMinMaxFPS();
		m_total_frame_time = 0.0;
		m_total_frames = 0;
	}

	bool UIFPSCounter::IsFrameRateLimited() const noexcept
	{
		std::lock_guard<std::mutex> lock(m_mutex);
		return m_target_fps > 0.0f;
	}

	void UIFPSCounter::UpdateStats() noexcept
	{
		if (m_frame_count == 0)
			return;

		const auto current_time = Clock::now();
		const auto elapsed = current_time - m_last_fps_update;
		const auto elapsed_seconds = std::chrono::duration<double>(elapsed).count();

		if (elapsed_seconds > 0.0)
		{
			m_current_fps = static_cast<float>(m_frame_count / elapsed_seconds);

			// 验证FPS值的有效性
			if (m_current_fps >= MIN_VALID_FPS && m_current_fps <= MAX_VALID_FPS)
			{
				m_min_fps = std::min(m_min_fps, m_current_fps);
				m_max_fps = std::max(m_max_fps, m_current_fps);
			}
		}

		m_frame_count = 0;
	}

	void UIFPSCounter::ResetMinMaxFPS() noexcept
	{
		m_min_fps = std::numeric_limits<float>::max();
		m_max_fps = 0.0f;
	}

} // namespace HHBUI

// ============================================================================
// Legacy Implementation (Deprecated)
// ============================================================================

float HHBUI::UIFPSCounter::CalcFPS()
{
	return MarkFrame();
}

void HHBUI::UIFPSCounter::SetMaxFPS(float fps)
{
	SetTargetFPS(fps);
}

float HHBUI::UIFPSCounter::GetMaxFPS()
{
	return GetTargetFPS();
}

void HHBUI::UIFPSCounter::LimitFPS()
{
	LimitFrameRate();
}


